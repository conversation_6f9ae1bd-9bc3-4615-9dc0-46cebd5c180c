#!/usr/bin/env python3
"""
Test script to verify that response length limitations have been fixed
"""

import requests
import json

def test_chat_response():
    """Test the chat endpoint to see if we get full responses"""
    
    # Test URL - adjust if your server runs on different port
    url = "http://localhost:5000/chat"
    
    # Test message that should generate a longer response
    test_message = "आपके यूट्यूब चैनल के लिए एक टोडो लिस्ट बनाने में मदद करने के लिए, यहाँ कुछ बातें हैं"
    
    payload = {
        "message": test_message,
        "crawl_websites": False  # Disable web search for faster testing
    }
    
    try:
        print("🧪 Testing chat response length...")
        print(f"📝 Test message: {test_message}")
        print("=" * 50)
        
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Response received successfully!")
            print(f"📊 Response length: {len(data['response'])} characters")
            print(f"🤖 API used: {data.get('api_used', 'Unknown')}")
            print(f"🧠 Model used: {data.get('model_used', 'Unknown')}")
            print(f"⚡ Response time: {data.get('response_time', 'Unknown')} seconds")
            print("=" * 50)
            print("📝 Full Response:")
            print(data['response'])
            print("=" * 50)
            
            # Check if response is truncated
            if data['response'].endswith('...'):
                print("❌ Response is still truncated with '...'")
                return False
            elif len(data['response']) < 50:
                print("⚠️  Response seems very short, might still be limited")
                return False
            else:
                print("✅ Response appears to be full length!")
                return True
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure the server is running on localhost:5000")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting response length test...")
    print("Make sure your Flask server is running on localhost:5000")
    print()
    
    success = test_chat_response()
    
    if success:
        print("\n🎉 Test PASSED: Response length limitation has been fixed!")
    else:
        print("\n❌ Test FAILED: Response is still being truncated")
